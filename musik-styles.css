/* Musik-Seite Spezifische Styles */

/* Breadcrumb Navigation */
.breadcrumb-container {
    background: #fff8ef;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.breadcrumb-link {
    color: #4b879a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #112736;
}

.breadcrumb-separator {
    margin: 0 10px;
    color: #999;
}

.breadcrumb-current {
    color: #112736;
    font-weight: 600;
}

/* Main Layout */
.musik-main {
    padding: 40px 0;
    min-height: calc(100vh - 200px);
}

.musik-header {
    text-align: center;
    margin-bottom: 40px;
}

.musik-title {
    font-size: 3rem;
    font-weight: 700;
    color: #112736;
    margin-bottom: 20px;
}

.musik-description {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Desktop Layout */
.desktop-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    margin-top: 40px;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #112736;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #4b879a;
}

.reset-button {
    padding: 8px 16px;
    background: #4b879a;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.reset-button:hover {
    background: #112736;
}

/* Song Table */
.song-table-wrapper {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 800px;
    overflow-y: auto;
    position: relative;
}

.song-table {
    width: 100%;
    border-collapse: collapse;
}

.song-table thead {
    background: #112736;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.song-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.song-table th.sortable:hover {
    background: #1a3a4a;
}

.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sort-indicator.asc {
    border-bottom: 6px solid white;
    opacity: 1;
}

.sort-indicator.desc {
    border-top: 6px solid white;
    opacity: 1;
}

.header-action-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.header-action-btn svg {
    width: 18px;
    height: 18px;
}

.ear-icon .strike-line {
    stroke: #ff4444;
    stroke-width: 3;
}

.song-table tbody tr {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.song-table tbody tr:hover {
    background: #f8f9fa;
}

.song-table tbody tr.playing {
    background: #e8f4f8;
}

.song-table tbody tr.hidden {
    opacity: 0.4;
    background: #f5f5f5;
}

.song-table td {
    padding: 12px;
    font-size: 14px;
    color: #333;
}

.song-table td:first-child {
    font-weight: 600;
    color: #4b879a;
    width: 40px;
}

.song-title {
    font-weight: 600;
    color: #112736;
}

.song-album, .song-genre, .song-sprache {
    color: #666;
    font-size: 13px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    color: #666;
}

.action-btn:hover {
    background: #f0f0f0;
    color: #112736;
}

.action-btn svg {
    width: 16px;
    height: 16px;
}

.hide-btn.active {
    color: #ff4444;
}

.hide-btn.active .strike-line {
    stroke: #ff4444;
}

/* Player Container */
.player-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.player-info {
    text-align: center;
    margin-bottom: 30px;
}

.album-cover-container {
    margin-bottom: 20px;
}

.album-cover {
    width: 280px;
    height: 280px;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.album-cover.has-cover {
    background: none;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.album-cover:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.placeholder-icon {
    width: 80px;
    height: 80px;
    color: #999;
}

.current-song-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #112736;
    margin-bottom: 5px;
}

.current-artist {
    font-size: 1rem;
    color: #4b879a;
    margin-bottom: 5px;
}

.current-album {
    font-size: 0.9rem;
    color: #666;
}

.album-link {
    color: #4b879a;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.album-link:hover {
    color: #112736;
    text-decoration: underline;
}

/* Player Controls */
.player-controls {
    margin-bottom: 30px;
}

.control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.control-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #112736;
}

.control-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.play-pause-btn {
    width: 60px;
    height: 60px;
    background: #4b879a;
    color: white;
}

.play-pause-btn:hover {
    background: #112736;
}

.control-btn svg {
    width: 24px;
    height: 24px;
}

.play-pause-btn svg {
    width: 28px;
    height: 28px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.time-display {
    font-size: 12px;
    color: #666;
    min-width: 35px;
}

.progress-bar-container {
    flex: 1;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    position: relative;
    cursor: pointer;
}

.progress-bar {
    width: 100%;
    height: 100%;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #4b879a;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    width: 14px;
    height: 14px;
    background: #4b879a;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-bar-container:hover .progress-handle {
    opacity: 1;
}

.additional-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.additional-controls .control-btn {
    width: 40px;
    height: 40px;
}

.additional-controls .control-btn svg {
    width: 20px;
    height: 20px;
}

.control-btn.active {
    background: #4b879a;
    color: white;
}

/* Lyrics */
.lyrics-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.lyrics-header {
    margin-bottom: 15px;
}

.lyrics-tabs {
    display: flex;
    gap: 2px;
    background: transparent;
    border-bottom: 1px solid #e9ecef;
}

.lyrics-tab {
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.lyrics-tab:hover {
    color: #4b879a;
    border-bottom-color: #dee2e6;
}

.lyrics-tab.active {
    color: #4b879a;
    border-bottom-color: #4b879a;
    font-weight: 600;
}

.lyrics-tab[style*="display: none"] {
    display: none !important;
}

.lyrics-content {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 10px;
    box-sizing: border-box;
}

.lyrics-content p {
    margin-bottom: 15px;
    margin-top: 0;
}

.lyrics-content p:last-child {
    margin-bottom: 0;
}

.no-lyrics {
    color: #999;
    font-style: italic;
}

/* Responsive Lyrics Tabs */
@media (max-width: 768px) {
    .lyrics-tabs {
        justify-content: flex-start;
    }

    .lyrics-tab {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Mobile Layout */
.mobile-layout {
    display: none;
}

.mobile-filter-controls {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 8px;
    margin-bottom: 15px;
    padding: 0 5px;
    align-items: center;
}

.mobile-filter-controls .filter-select {
    font-size: 16px; /* Verhindert Zoom auf iOS */
    min-width: 0; /* Ermöglicht Flexibilität */
}

.mobile-reset-button {
    background: #f0f0f0;
    border: none;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
}

.mobile-reset-button:hover {
    background: #e0e0e0;
    color: #4b879a;
}

.mobile-reset-button:active {
    transform: scale(0.95);
}

/* Mobile Control Buttons */
.mobile-control-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 5px;
}

.mobile-control-btn {
    flex: 1;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.mobile-control-btn:hover {
    border-color: #4b879a;
    color: #4b879a;
    background: #f8fbfc;
}

.mobile-control-btn.active {
    background: #4b879a;
    border-color: #4b879a;
    color: white;
}

.mobile-control-btn svg {
    flex-shrink: 0;
}

.mobile-control-btn span {
    font-weight: 600;
}

/* Mobile Song List */
.mobile-song-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.mobile-song-item:hover {
    transform: translateY(-2px);
}

.mobile-song-item.playing {
    background: #e8f4f8;
    border-left: 4px solid #4b879a;
}

.mobile-song-cover {
    width: 50px;
    height: 50px;
    background: #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.mobile-song-cover.has-cover {
    background: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-song-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mobile-song-cover svg {
    width: 24px;
    height: 24px;
    color: #999;
}

.mobile-song-info {
    flex: 1;
    min-width: 0; /* Ermöglicht text-overflow */
}

.mobile-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 16px;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-song-actions {
    position: relative;
    flex-shrink: 0;
}

.mobile-song-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    color: #666;
}

.mobile-song-menu-btn:hover {
    background: #f0f0f0;
}

.mobile-song-menu-btn svg {
    width: 20px;
    height: 20px;
}

/* Dropdown Menu */
.mobile-song-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.mobile-song-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-song-dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 14px;
    color: #333;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.mobile-song-dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.mobile-song-dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

.mobile-song-dropdown-item:hover {
    background: #f8f9fa;
}

.mobile-song-dropdown-item svg {
    width: 16px;
    height: 16px;
    color: #666;
}

.mobile-song-dropdown-item .ear-icon .strike-line {
    stroke: #ff4444;
    stroke-width: 2;
}

/* Mini Player */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mini-player-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.mini-album-cover {
    width: 40px;
    height: 40px;
    background: #f0f0f0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mini-album-cover.has-cover {
    background: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mini-album-cover svg {
    width: 20px;
    height: 20px;
    color: #999;
}

.mini-song-info {
    flex: 1;
}

.mini-song-title {
    font-weight: 600;
    color: #112736;
    font-size: 14px;
    display: block;
}

.mini-artist {
    color: #666;
    font-size: 12px;
}

.mini-controls {
    display: flex;
    gap: 10px;
}

.mini-control-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    color: #112736;
}

.mini-control-btn:hover {
    background: #f0f0f0;
}

.mini-control-btn svg {
    width: 20px;
    height: 20px;
}

/* Modal Player */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-player {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: #f0f0f0;
}

.modal-close svg {
    width: 24px;
    height: 24px;
    color: #666;
}

.modal-content {
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .desktop-layout {
        grid-template-columns: 1fr 350px;
        gap: 30px;
    }
    
    .player-container {
        padding: 20px;
    }
    
    .album-cover {
        width: 220px;
        height: 220px;
    }
}

@media (max-width: 768px) {
    .desktop-layout {
        display: none;
    }
    
    .mobile-layout {
        display: block;
        margin-top: 20px;
    }
    
    .musik-title {
        font-size: 2rem;
    }
    
    .musik-description {
        font-size: 1rem;
        padding: 0 20px;
    }
    
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .musik-main {
        padding: 20px 0;
    }

    .musik-title {
        font-size: 1.8rem;
    }

    .breadcrumb-container {
        padding: 15px 0;
    }

    .container {
        padding: 0 15px;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Loading States */
.song-table.loading tbody {
    opacity: 0.5;
}

.player-container.loading {
    opacity: 0.7;
}

/* Focus States for Accessibility */
.control-btn:focus,
.action-btn:focus,
.header-action-btn:focus {
    outline: 2px solid #4b879a;
    outline-offset: 2px;
}

.song-table tbody tr:focus {
    outline: 2px solid #4b879a;
    outline-offset: -2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .song-table th {
        border: 1px solid #000;
    }

    .song-table td {
        border: 1px solid #666;
    }

    .control-btn {
        border: 2px solid #000;
    }
}
